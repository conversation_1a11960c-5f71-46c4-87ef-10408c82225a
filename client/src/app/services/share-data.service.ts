import { Injectable } from '@angular/core';
import { Track } from '@/interfaces/track';
import { BehaviorSubject } from 'rxjs';

export interface PlayerState {
  currentTrack: Track | null;
  currentTrackIndex: number;
  currentTime: number;
  queue: Track[];
  isPlaying: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class ShareDataService {
  addToPlaylist$: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  playCard$: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  playMainPlayer$: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  playRadio$: BehaviorSubject<any> = new BehaviorSubject<any>(null);
  changeTracks$: BehaviorSubject<any> = new BehaviorSubject<null>(null);
  showInfoModal$: BehaviorSubject<any> = new BehaviorSubject<null>(null);

  addToPlaylist(track: Track, play?: boolean) {
    this.addToPlaylist$.next({track, play});
  }

  playCard() {
    this.playCard$.next(true);
  }

  playMainPlayer() {
    this.playMainPlayer$.next(true);
  }

  playRadio(track: any, play?: boolean) {
    let radio: Track = {
      link: track,
      audioStatus: '',
      author: 'Радио',
      comment: 'Радио',
      date: '',
      description: 'Радио',
      duration: '∞',
      fullDescription: '',
      external_id: '',
      scriptures: {},
      title: 'Радио',
      type: '',
      videoStatus: 'false',
      youtube: '',
      views: 0,
      likes: 0,
      id: 235435657548,
      liked: false,
      inFavourites: false,
  }
    this.playRadio$.next({radio, play});
  }

  showInfoModal(message: string) {
    this.showInfoModal$.next(message);
  }

  changePlaylist(items: Track[]) {
    this.changeTracks$.next(items);
  }

  // LocalStorage methods for player state
  private readonly PLAYER_STATE_KEY = 'advayta_player_state';

  savePlayerState(state: PlayerState): void {
    try {
      localStorage.setItem(this.PLAYER_STATE_KEY, JSON.stringify(state));
    } catch (error) {
      console.error('Error saving player state to localStorage:', error);
    }
  }

  loadPlayerState(): PlayerState | null {
    try {
      const savedState = localStorage.getItem(this.PLAYER_STATE_KEY);
      if (savedState) {
        return JSON.parse(savedState);
      }
    } catch (error) {
      console.error('Error loading player state from localStorage:', error);
    }
    return null;
  }

  clearPlayerState(): void {
    try {
      localStorage.removeItem(this.PLAYER_STATE_KEY);
    } catch (error) {
      console.error('Error clearing player state from localStorage:', error);
    }
  }

  updatePlayerTime(currentTime: number): void {
    const savedState = this.loadPlayerState();
    if (savedState) {
      savedState.currentTime = currentTime;
      this.savePlayerState(savedState);
    }
  }

  updatePlayerQueue(queue: Track[], currentTrackIndex: number): void {
    const savedState = this.loadPlayerState();
    if (savedState) {
      savedState.queue = queue;
      savedState.currentTrackIndex = currentTrackIndex;
      savedState.currentTrack = queue[currentTrackIndex] || null;
      this.savePlayerState(savedState);
    }
  }
}
