import { HttpClient } from '@angular/common/http'
import { Injectable, inject } from '@angular/core'
import { BehaviorSubject } from 'rxjs'
import { Chat, ChatMessage, ChatSource } from '../pages/ai-chat/ai-chat.component'

export interface AiResponse {
  aiResponse?: {
    message: string;
    metadata?: any;
    sources?: ChatSource[];
  };
  assistantMessage?: {
    chatId: string;
    content: string;
    id: string;
    role: string;
    timestamp: string;
    isTyping: boolean;
  };
  userMessage?: {
    chatId: string;
    content: string;
    id: string;
    role: string;
    timestamp: string;
    isTyping: boolean;
  };
  content?: string;
  sources?: ChatSource[];
}

@Injectable({
  providedIn: 'root'
})
export class AiChatService {
  private http = inject(HttpClient);
  private chatsSubject = new BehaviorSubject<Chat[]>([]);

  constructor() {
    this.loadChats();
  }

  getChats() {
    return this.chatsSubject.asObservable();
  }

  loadChats(): void {
    this.http.get<Chat[]>('/client/ai-chat').subscribe((chats) => {
      this.chatsSubject.next(chats);
    });
  }

  createNewChat() {
    return this.http.post<Chat>('/client/ai-chat', {});
  }

  updateChat(chatId: string, updates: Partial<Chat>) {
    return this.http.patch<Chat>(`/client/ai-chat/${chatId}`, updates);
  }

  deleteChat(chatId: string) {
    return this.http.delete<void>(`/client/ai-chat/${chatId}`);
  }

  sendMessage(chatId: string, message: string) {
    return this.http.post<any>(`/client/ai-chat/${chatId}/messages`, { content: message });
  }

  getChatMessages(chatId: string) {
    return this.http.get<ChatMessage[]>(`/client/ai-chat/${chatId}/messages`);
  }

}
