import { CommonModule } from '@angular/common'
import { ChangeDetectionStrategy, Component, effect, input, OnDestroy, output, signal } from '@angular/core'
import { ChatMessage, ChatSource } from '../../ai-chat.component'

@Component({
  selector: 'app-chat-message',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './chat-message.component.html',
  styleUrl: './chat-message.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ChatMessageComponent implements OnDestroy {
  // Inputs
  message = input.required<ChatMessage>();
  isLoading = input<boolean>(false);

  // Outputs
  sourceClick = output<ChatSource>();
  typewritingComplete = output<string>();

  // State
  isCopied = signal<boolean>(false);
  displayedContent = signal<string>('');
  private typewriterInterval?: number;

  constructor() {
    effect(() => {
      const message = this.message();
      if (message.isTypewriting && message.fullContent) {
        this.startTypewriter(message.fullContent);
      } else {
        this.displayedContent.set(message.content);
      }
    });
  }

  ngOnDestroy() {
    if (this.typewriterInterval) {
      clearInterval(this.typewriterInterval);
    }
  }

  private startTypewriter(fullText: string) {
    if (this.typewriterInterval) {
      clearInterval(this.typewriterInterval);
    }

    let currentIndex = 0;
    this.displayedContent.set('');

    this.typewriterInterval = window.setInterval(() => {
      if (currentIndex < fullText.length) {
        this.displayedContent.update(current => current + fullText[currentIndex]);
        currentIndex++;
      } else {
        if (this.typewriterInterval) {
          clearInterval(this.typewriterInterval);
          this.typewriterInterval = undefined;
        }
        this.typewritingComplete.emit(this.message().id);
      }
    }, 30);
  }

  onCopyMessage() {
    const message = this.message();
    const content = message.fullContent || message.content;
    if (!content.trim()) return;

    navigator.clipboard.writeText(content).then(() => {
      this.isCopied.set(true);
      setTimeout(() => {
        this.isCopied.set(false);
      }, 2000);
    }).catch(err => {
      console.error('Failed to copy text: ', err);
    });
  }

  onSourceClick(source: ChatSource) {
    this.sourceClick.emit(source);
  }

  formatTime(date: Date): string {
    return new Date(date).toLocaleTimeString('ru-RU', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // Parse message content to handle markdown and citations
  parseContent(content: string): { type: 'text' | 'citation' | 'source'; content: string; source?: ChatSource }[] {
    if (!content) return [];

    const parts: { type: 'text' | 'citation' | 'source'; content: string; source?: ChatSource }[] = [];
    const sources = this.message().sources || [];
    
    // Simple parsing for citations [1], [2], etc.
    const citationRegex = /\[(\d+)\]/g;
    let lastIndex = 0;
    let match;

    while ((match = citationRegex.exec(content)) !== null) {
      // Add text before citation
      if (match.index > lastIndex) {
        const textContent = content.slice(lastIndex, match.index);
        if (textContent) {
          parts.push({ type: 'text', content: textContent });
        }
      }

      // Add citation
      const citationNumber = parseInt(match[1]) - 1;
      const source = sources[citationNumber];
      if (source) {
        parts.push({ 
          type: 'citation', 
          content: match[0],
          source: source
        });
      } else {
        parts.push({ type: 'text', content: match[0] });
      }

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < content.length) {
      const remainingContent = content.slice(lastIndex);
      if (remainingContent) {
        parts.push({ type: 'text', content: remainingContent });
      }
    }

    return parts.length > 0 ? parts : [{ type: 'text', content: content }];
  }

  // Format markdown-like content
  formatMarkdown(text: string): string {
    return text
      // Bold text **text**
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      // Italic text *text*
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // Code blocks ```code```
      .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
      // Inline code `code`
      .replace(/`(.*?)`/g, '<code>$1</code>')
      // Line breaks
      .replace(/\n/g, '<br>');
  }
}
